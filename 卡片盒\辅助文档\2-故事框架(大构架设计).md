# 2. 故事框架（大构架设计）

有了前期准备的基础,接下来要着手设计故事的整体框架,确立故事的骨干。本阶段包括以下两个任务:

## 2.1 设计主线剧情
- 确定故事起点和终点
- 规划3-5个重要转折点  
- 设计结局

**示例**：
```
起点：主角穿越到大周王朝，成为一个落魄世家子弟
转折点：
1. 发现自己拥有前世知识带来的特殊能力
2. 被卷入皇权争斗
3. 获得重要密令
4. 面临重大抉择
结局：成功化解危机，但付出巨大代价
```

## 2.2 规划卷与幕的结构
- 将故事分为几个大的阶段(卷)
- 每卷细分为几个幕
- 确定各幕的核心冲突

**示例**：
```
第一卷：适应与崛起
- 第一幕：初入异世
- 第二幕：展露才华 
- 第三幕：初涉权谋

第二卷：权力与挣扎
...
```

> 提示：大构架要体现整个故事的起承转合,照应前期准备中确立的主题。转折点和冲突是故事的动力,要分布得当,环环相扣。 